package cn.com.chinastock.cnf.examples.dto;

import java.util.List;

/**
 * 批量消息响应DTO
 */
public class BatchMessageResponse {
    
    private int totalCount;
    private int successCount;
    private int failureCount;
    private List<MessageResult> results;
    
    public BatchMessageResponse() {
    }
    
    public BatchMessageResponse(int totalCount, int successCount, int failureCount, List<MessageResult> results) {
        this.totalCount = totalCount;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.results = results;
    }
    
    public int getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
    
    public int getSuccessCount() {
        return successCount;
    }
    
    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }
    
    public int getFailureCount() {
        return failureCount;
    }
    
    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }
    
    public List<MessageResult> getResults() {
        return results;
    }
    
    public void setResults(List<MessageResult> results) {
        this.results = results;
    }
    
    @Override
    public String toString() {
        return "BatchMessageResponse{" +
                "totalCount=" + totalCount +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", results=" + results +
                '}';
    }
} 