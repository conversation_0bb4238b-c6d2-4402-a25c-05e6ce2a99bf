package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.examples.dto.BatchMessageRequest;
import cn.com.chinastock.cnf.examples.dto.BatchMessageResponse;
import cn.com.chinastock.cnf.examples.dto.MessageResult;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池测试控制器
 * 用于测试在多线程处理的情况下，是否可以正常打印TraceId等信息
 */
@RestController
@RequestMapping("/api/test/thread-pool")
public class ThreadPoolController {

    @Autowired
    private BeanFactory beanFactory;

    private final ExecutorService executorService;

    public ThreadPoolController(BeanFactory beanFactory) {
        this.beanFactory = beanFactory;
        ThreadPoolExecutor delegateExecutor = new ThreadPoolExecutor(
                5,
                10,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread thread = new Thread(r, "thread-pool-" + threadNumber.getAndIncrement());
                        thread.setDaemon(false);
                        return thread;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        this.executorService = new ContextPropagatingExecutor(delegateExecutor, beanFactory);
    }

    /**
     * 批量发送消息API
     * 通过线程池处理消息，并汇总处理结果
     * 
     * @param request 批量消息请求
     * @return 批量消息处理结果
     */
    @PostMapping("/batch-message")
    public BaseResponse<BatchMessageResponse> batchMessage(@RequestBody BatchMessageRequest request) {
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "开始批量处理消息，消息数量: {}", request.getMessages().size());

        List<String> messages = request.getMessages();
        List<MessageResult> results = new ArrayList<>();
        AtomicInteger taskIdCounter = new AtomicInteger(1);

        try {
            // 提交所有任务到线程池
            List<Future<MessageResult>> futures = new ArrayList<>();
            for (String message : messages) {
                int taskId = taskIdCounter.getAndIncrement();
                Future<MessageResult> future = executorService.submit(() -> processMessage(taskId, message));
                futures.add(future);
            }

            // 收集所有任务的结果
            for (Future<MessageResult> future : futures) {
                try {
                    MessageResult result = future.get(30, TimeUnit.SECONDS); // 30秒超时
                    results.add(result);
                } catch (TimeoutException e) {
                    GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "任务执行超时", e);
                    results.add(new MessageResult(taskIdCounter.get(), "任务超时", false, "执行超时"));
                } catch (Exception e) {
                    GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "任务执行异常", e);
                    results.add(new MessageResult(taskIdCounter.get(), "任务异常", false, e.getMessage()));
                }
            }

            // 统计结果
            int successCount = (int) results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).count();
            int failureCount = results.size() - successCount;

            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "批量消息处理完成，成功: {}, 失败: {}", successCount, failureCount);

            BatchMessageResponse response = new BatchMessageResponse(
                    results.size(), successCount, failureCount, results
            );

            return new BaseResponse<>(new Meta(true, "0", "批量消息处理完成"), response);

        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "批量消息处理失败", e);
            return new BaseResponse<>(new Meta(false, "500", "批量消息处理失败: " + e.getMessage()), null);
        }
    }

    /**
     * 处理单个消息
     * 
     * @param taskId 任务ID
     * @param message 消息内容
     * @return 处理结果
     */
    private MessageResult processMessage(int taskId, String message) {
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "线程池任务开始处理，任务ID: {}, 消息: {}", taskId, message);

        try {
            // 模拟处理时间
            Thread.sleep(100 + (long) (Math.random() * 200));

            String result = "已处理: " + message + " (任务ID: " + taskId + ")";
            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "线程池任务处理完成，任务ID: {}, 处理结果: {}", taskId, result);

            return new MessageResult(taskId, result, true, null);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "线程池任务被中断，任务ID: {}", taskId, e);
            return new MessageResult(taskId, message, false, "任务被中断");

        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "线程池任务处理异常，任务ID: {}", taskId, e);
            return new MessageResult(taskId, message, false, e.getMessage());
        }
    }
} 