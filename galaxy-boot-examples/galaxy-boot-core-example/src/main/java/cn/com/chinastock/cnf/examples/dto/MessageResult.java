package cn.com.chinastock.cnf.examples.dto;

/**
 * 消息处理结果DTO
 */
public class MessageResult {
    
    private int taskId;
    private String message;
    private boolean success;
    private String errorMessage;
    
    public MessageResult() {
    }
    
    public MessageResult(int taskId, String message, boolean success, String errorMessage) {
        this.taskId = taskId;
        this.message = message;
        this.success = success;
        this.errorMessage = errorMessage;
    }
    
    public int getTaskId() {
        return taskId;
    }
    
    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    @Override
    public String toString() {
        return "MessageResult{" +
                "taskId=" + taskId +
                ", message='" + message + '\'' +
                ", success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
} 